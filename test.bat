python generate.py test --model cyberrealisticxl --sampler dpmpp_2m_sde --guidance 5.0,6.0,7.0,8.0,9.0 --page animals-wildlife

python generate.py test --model cyberrealisticxl --sampler dpmpp_2m_sde --guidance 5.0,6.0,7.0,8.0,9.0 --page art-mindfulness

python generate.py test --model cyberrealisticxl --sampler dpmpp_2m_sde --guidance 5.0,6.0,7.0,8.0,9.0 --page fantasy-sci-fi

python generate.py test --model cyberrealisticxl --sampler dpmpp_2m_sde --guidance 5.0,6.0,7.0,8.0,9.0 --page food-fun

python generate.py test --model cyberrealisticxl --sampler dpmpp_2m_sde --guidance 5.0,6.0,7.0,8.0,9.0 --page holidays-seasons

python generate.py test --model cyberrealisticxl --sampler dpmpp_2m_sde --guidance 5.0,6.0,7.0,8.0,9.0 --page learning-development

python generate.py test --model cyberrealisticxl --sampler dpmpp_2m_sde --guidance 5.0,6.0,7.0,8.0,9.0 --page nature-landscapes

python generate.py test --model cyberrealisticxl --sampler dpmpp_2m_sde --guidance 5.0,6.0,7.0,8.0,9.0 --page people-professions

python generate.py test --model cyberrealisticxl --sampler dpmpp_2m_sde --guidance 5.0,6.0,7.0,8.0,9.0 --page vehicles-transportation
