import os
import csv
import shutil
import argparse
import time
import sys
import random

# --- CONFIGURATION CONSTANTS ---
# File/Folder Paths
SRC_DIR = 'src'
SD_MODELS_DIR = 'c:/EasyDiffusion/models'
CATEGORIES_CSV = 'categories.csv'
PAGES_CSV = 'pages.csv'

# Stable Diffusion Model Paths
SD_MODELS_DIR_PATH = {
    'sdxl': os.path.join(SD_MODELS_DIR, 'stable-diffusion/sd_xl_base_1.0.safetensors'),
    'cyberrealisticxl': os.path.join(SD_MODELS_DIR, 'stable-diffusion/cyberrealisticXL_v60.safetensors'),
    'dreamshaperxl': os.path.join(SD_MODELS_DIR, 'stable-diffusion/dreamshaperXL_v21TurboDPMSDE.safetensors'),
    'juggernautxl': os.path.join(SD_MODELS_DIR, 'stable-diffusion/juggernautXL_ragnarokBy.safetensors'),
    'realvisxl': os.path.join(SD_MODELS_DIR, 'stable-diffusion/realvisxlV50_v50LightningBakedvae.safetensors')
}

VAE_PATH = os.path.join(SD_MODELS_DIR, 'vae/sdxl_vae.safetensors')
LORA_PATH = os.path.join(SD_MODELS_DIR, 'lora/ColoringBookRedmond-ColoringBook-ColoringBookAF.safetensors')

# Default Stable Diffusion Settings
SD_DEFAULTS = {
    # Coloring page defaults
    'model': 'sdxl',
    'sampler': 'dpmpp_2m',
    'steps': 30,
    'guidance': 7.5,
    'lora_scale': 0.9,
    # Category thumbnail defaults
    'model_thumb': 'cyberrealisticxl',
    'sampler_thumb': 'dpmpp_2m_sde',
    'steps_thumb': 25,
    'guidance_thumb': 5.0,
}

# Image Orientation Resolutions
ORIENTATIONS = {
    'landscape': (1216, 832),
    'portrait': (832, 1216),
    'square': (1024, 1024),
}

# Prompt Templates
PAGE_POSITIVE_PROMPT = "coloring page of {subject}, {camera_angle}, {camera_shot}, coloring book style, line art, thin lines, clean outlines, clean vector style, minimalist, black and white, simple background, white background"

PAGE_NEGATIVE_PROMPT = "color, shading, gradient, shadows, shades, transparency, blurry, watermark, text, details, complex background, cropped, deformed, scanlines, tramlines, thick lines, hatch lines, crosshatching, dither, texture, noisy, noise, artifacts, ornaments, scribble, gray, gray fill, gray gradient, grayscale, dark, adult content, nude, naked, nsfw, extra arms, extra legs"

CATEGORY_POSITIVE_PROMPT = "masterpiece, anime style, colorful illustration of {prompt}, vibrant colors, children's book style, friendly and welcoming, bright and cheerful, simple composition, professional thumbnail design, high quality digital art, high resolution"

CATEGORY_NEGATIVE_PROMPT = "dark, gloomy, scary, realistic, photograph, blurry, low quality, watermark, text, signature, complex background, cluttered, crowded, too many details, adult content, nude, naked, nsfw, cropped, deformed, noisy, noise, artifacts, mixed animals, fused animals, blended animals, hybrid animals, morphed animals, morphing, fused features, blended features, extra arms, extra legs"

# --- DATA LOADING FUNCTIONS ---
def load_data_from_csv(filepath):
    parent_categories = {}
    subcategories_to_process = []
    try:
        with open(filepath, mode='r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                if not row['parent-category']:
                    category_slug = row['category']
                    parent_categories[category_slug] = {
                        "slug": category_slug, "name": row['display-name'], "description": row['description'],
                        "related": row['related-categories'].split(',') if row['related-categories'] else [],
                        "content": row['content'], "prompt": row.get('prompt', ''), "subcategories": []
                    }
                else:
                    subcategories_to_process.append(row)
    except FileNotFoundError:
        print(f"Error: The file '{filepath}' was not found.")
        return None
    for sub_row in subcategories_to_process:
        parent_slug = sub_row['parent-category']
        if parent_slug in parent_categories:
            parent_categories[parent_slug]['subcategories'].append({
                "slug": sub_row['category'], "name": sub_row['display-name'], "description": sub_row['description'],
                "related": sub_row['related-categories'].split(',') if sub_row['related-categories'] else [],
                "content": sub_row['content'], "prompt": sub_row.get('prompt', '')
            })
        else:
            print(f"Warning: Parent category '{parent_slug}' not found for subcategory '{sub_row['category']}'.")
    return list(parent_categories.values())

def load_pages_data_from_csv(filepath):
    pages = []
    try:
        with open(filepath, mode='r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                pages.append(row)
        return pages
    except FileNotFoundError:
        print(f"Error: The file '{filepath}' was not found.")
        return None

# --- FILE & DIRECTORY SYNC FUNCTIONS ---
def sync_category_markdown(base_path, categories):
    print(f"\n--- Syncing Category Markdown Structure in '{base_path}' ---")
    os.makedirs(base_path, exist_ok=True)
    for category in categories:
        os.makedirs(os.path.join(base_path, category["slug"]), exist_ok=True)
        parent_file_path = os.path.join(base_path, f'{category["slug"]}.md')
        related_string = "[" + ", ".join([f'"{s}"' for s in category.get("related", []) if s]) + "]"
        with open(parent_file_path, 'w', encoding='utf-8') as f:
            f.write(f'---\nname: "{category["name"]}"\ndescription: "{category["description"]}"\nrelatedCategories: {related_string}\n---\n{category["content"]}\n')
        for subcategory in category["subcategories"]:
            sub_file_path = os.path.join(base_path, category["slug"], f'{subcategory["slug"]}.md')
            related_string = "[" + ", ".join([f'"{s}"' for s in subcategory.get("related", []) if s]) + "]"
            with open(sub_file_path, 'w', encoding='utf-8') as f:
                f.write(f'---\nname: "{subcategory["name"]}"\ndescription: "{subcategory["description"]}"\nrelatedCategories: {related_string}\n---\n{subcategory["content"]}\n')

def sync_page_markdown(base_path, pages_data):
    print(f"\n--- Syncing Coloring Page Markdown Files in '{base_path}' ---")
    if not pages_data: return
    for page in pages_data:
        page_dir = os.path.join(base_path, page['parent-category'], page['category'])
        os.makedirs(page_dir, exist_ok=True)
        file_path = os.path.join(page_dir, f"{page['slug']}.md")
        title = page['title'].replace('"', '\\"')
        description = page['description'].replace('"', '\\"')
        prompt = page['prompt'].replace('"', '\\"')
        yaml_content = f"""---
title: "{title}"
description: "{description}"
difficulty: "{page['difficulty']}"
orientation: "{page['orientation']}"
---
{page['content']}
"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(yaml_content)

def sync_directory_structure(base_path, categories):
    print(f"\n--- Syncing Directory Structure in '{base_path}' ---")
    os.makedirs(base_path, exist_ok=True)
    for category in categories:
        os.makedirs(os.path.join(base_path, category['slug']), exist_ok=True)
        for subcategory in category['subcategories']:
            os.makedirs(os.path.join(base_path, category['slug'], subcategory['slug']), exist_ok=True)

def get_expected_paths(categories, pages, md_base, coloring_pages_base):
    expected_md, expected_coloring = set(), set()
    for cat in categories:
        expected_md.add(os.path.normpath(os.path.join(md_base, f"{cat['slug']}.md")))
        for base_path, path_set in [(md_base, expected_md), (coloring_pages_base, expected_coloring)]:
            path_set.add(os.path.normpath(os.path.join(base_path, cat['slug'])))
        for sub in cat['subcategories']:
            expected_md.add(os.path.normpath(os.path.join(md_base, cat['slug'], f"{sub['slug']}.md")))
            path_set = expected_coloring
            path_set.add(os.path.normpath(os.path.join(base_path, cat['slug'], sub['slug'])))
    if pages:
        for page in pages:
            page_path = os.path.join(coloring_pages_base, page['parent-category'], page['category'], f"{page['slug']}.md")
            expected_coloring.add(os.path.normpath(page_path))
    return expected_md, expected_coloring

def get_expected_image_paths(category_data, pages_data, image_base_path):
    """Generates a set of all valid image file paths, including variants."""
    expected_paths = set()
    # Add category thumbnail paths
    for cat in category_data:
        expected_paths.add(os.path.normpath(os.path.join(image_base_path, f"{cat['slug']}.png")))
        for sub in cat['subcategories']:
            expected_paths.add(os.path.normpath(os.path.join(image_base_path, cat['slug'], f"{sub['slug']}.png")))
    # Add page image paths
    if pages_data:
        for page in pages_data:
            # This logic assumes a max of 9 variants, which is reasonable.
            for i in range(10):
                variant_suffix = "" if i == 0 else f"-{i+1}"
                path = os.path.join(image_base_path, page['parent-category'], page['category'], f"{page['slug']}{variant_suffix}.png")
                expected_paths.add(os.path.normpath(path))
    return expected_paths

def cleanup_orphans(base_path, expected_paths):
    print(f"\n--- Cleaning orphaned files/folders in '{base_path}' ---")
    if not os.path.exists(base_path): return
    for root, dirs, files in os.walk(base_path, topdown=False):
        for name in files:
            file_path = os.path.normpath(os.path.join(root, name))
            if file_path not in expected_paths:
                print(f"Removing orphaned file: {file_path}")
                os.remove(file_path)
        for name in dirs:
            dir_path = os.path.normpath(os.path.join(root, name))
            if dir_path not in expected_paths:
                print(f"Removing orphaned directory: {dir_path}")
                shutil.rmtree(dir_path)

def cleanup_orphan_images(base_path, expected_image_paths):
    print(f"\n--- Cleaning orphaned images in '{base_path}' ---")
    if not os.path.exists(base_path): return
    for root, dirs, files in os.walk(base_path, topdown=False):
        for name in files:
            if name.endswith('.png'):
                file_path = os.path.normpath(os.path.join(root, name))
                if file_path not in expected_image_paths:
                    print(f"Removing orphaned image: {file_path}")
                    os.remove(file_path)
        if os.path.exists(root) and not os.listdir(root):
            print(f"Removing empty image directory: {root}")
            os.rmdir(root)

# --- IMAGE GENERATION FUNCTIONS ---
def generate_page_images(context, pages_data, image_base_path, variants, steps, guidance, lora_scale, sampler_name, force_overwrite, seed):
    from sdkit.generate import generate_images
    print("\n--- Generating Coloring Page Images ---")
    generated_count = 0
    for i, page in enumerate(pages_data):
        print(f"\nProcessing page {i+1}/{len(pages_data)}: {page['slug']}")
        target_dir = os.path.join(image_base_path, page['parent-category'], page['category'])
        base_image_path = os.path.join(target_dir, f"{page['slug']}.png")
        if os.path.exists(base_image_path) and not force_overwrite:
            print(f"  -> Skipping: Base image already exists.")
            continue
        generated_count += 1
        positive_prompt = PAGE_POSITIVE_PROMPT.format(subject=page['prompt'], camera_angle=page['camera-angle'], camera_shot=page['camera-shot'])
        width, height = ORIENTATIONS.get(page.get('orientation', 'portrait').lower(), ORIENTATIONS['portrait'])
        print(f"  -> Generating {variants} variant(s) for '{page['slug']}' at {width}x{height}...")
        images = generate_images(
            context,
            prompt=positive_prompt,
            negative_prompt=PAGE_NEGATIVE_PROMPT,
            num_inference_steps=steps,
            guidance_scale=guidance,
            lora_alpha=lora_scale,
            width=width,
            height=height,
            num_outputs=variants,
            sampler_name=sampler_name,
            seed=seed if seed is not None else random.randint(0, 2**32 - 1),
        )
        for idx, img in enumerate(images):
            save_path = base_image_path if idx == 0 else os.path.join(target_dir, f"{page['slug']}-{idx + 1}.png")
            img.save(save_path)
            print(f"  -> Saved: {save_path}")
    return generated_count

def generate_category_thumbnails(context, category_data, image_base_path, variants, steps_thumb, guidance, sampler_name, force_overwrite, seed):
    from sdkit.generate import generate_images
    print("\n--- Generating Category Thumbnails ---")
    generated_count = 0
    for cat in category_data:
        # Process parent category
        if cat['prompt']:
            path = os.path.join(image_base_path, f"{cat['slug']}.png")
            if not os.path.exists(path) or force_overwrite:
                generated_count += 1
                prompt = CATEGORY_POSITIVE_PROMPT.format(prompt=cat['prompt'])
                print(f"  -> Generating {variants} variant(s) for category '{cat['slug']}'...")
                images = generate_images(
                    context,
                    prompt=prompt,
                    negative_prompt=CATEGORY_NEGATIVE_PROMPT,
                    num_inference_steps=steps_thumb,
                    guidance_scale=guidance,
                    width=1024,
                    height=1024,
                    num_outputs=variants,
                    sampler_name=sampler_name,
                    seed=seed if seed is not None else random.randint(0, 2**32 - 1),
                )
                for idx, img in enumerate(images):
                    save_path = path if idx == 0 else os.path.join(image_base_path, f"{cat['slug']}-{idx + 1}.png")
                    img.save(save_path)
                    print(f"  -> Saved: {save_path}")
        # Process subcategories
        for sub in cat['subcategories']:
            if sub['prompt']:
                path = os.path.join(image_base_path, cat['slug'], f"{sub['slug']}.png")
                if not os.path.exists(path) or force_overwrite:
                    generated_count += 1
                    prompt = CATEGORY_POSITIVE_PROMPT.format(prompt=sub['prompt'])
                    print(f"  -> Generating {variants} variant(s) for subcategory '{sub['slug']}'...")
                    images = generate_images(
                        context,
                        prompt=prompt,
                        negative_prompt=CATEGORY_NEGATIVE_PROMPT,
                        num_inference_steps=steps_thumb,
                        guidance_scale=guidance,
                        width=1024,
                        height=1024,
                        num_outputs=variants,
                        sampler_name=sampler_name,
                        seed=seed if seed is not None else random.randint(0, 2**32 - 1),
                    )
                    for idx, img in enumerate(images):
                        save_path = path if idx == 0 else os.path.join(image_base_path, cat['slug'], f"{sub['slug']}-{idx + 1}.png")
                        img.save(save_path)
                        print(f"  -> Saved: {save_path}")
    return generated_count

def run_images_command(category_data, pages_data, variants, steps, guidance, lora_scale, sampler_name, model_name, force_overwrite, seed):
    import torch
    from sdkit import Context
    from sdkit.models import load_model
    device = "cuda" if torch.cuda.is_available() else "cpu"
    if device == "cpu":
        print("CUDA is not available. Image generation requires a GPU.")
        return

    print("\n--- Initializing Stable Diffusion Pipeline (this may take a moment) ---")
    context = Context()
    context.device = device
    context.half_precision = True  # Recommended for SDXL
    if seed is not None:
        torch.manual_seed(seed)
    try:
        context.model_paths["stable-diffusion"] = SD_MODELS_DIR_PATH[model_name]
        context.model_paths["vae"] = VAE_PATH
        load_model(context, "stable-diffusion")
        load_model(context, "vae")
        print("Pipeline initialized successfully.")
    except Exception as e:
        print(f"Error initializing Diffusion Pipeline: {e}")
        return

    image_base_path = os.path.join(SRC_DIR, 'assets', 'images')
    start_time = time.perf_counter()

    # 1. Generate page images
    print("\n--- Loading LoRA for page generation ---")
    context.model_paths["lora"] = LORA_PATH
    load_model(context, "lora")
    pages_generated = generate_page_images(context, pages_data, image_base_path, variants, steps, guidance, lora_scale, sampler_name, force_overwrite, seed)

    end_time = time.perf_counter()
    duration = end_time - start_time
    total_items_generated = pages_generated

    if total_items_generated > 0:
        minutes, seconds = divmod(duration, 60)
        print("\n--- Image Generation Complete ---")
        print(f"Generated a total of {total_items_generated * variants} coloring page image(s).")
        print(f"Total time taken: {int(minutes)} minutes and {seconds:.2f} seconds.")
    else:
        print("\n--- No new coloring page images to generate. All items are up to date. ---")

def run_thumbnails_command(category_data, variants, steps_thumb, guidance, sampler_name, model_name, force_overwrite, seed):
    import torch
    from sdkit import Context
    from sdkit.models import load_model
    device = "cuda" if torch.cuda.is_available() else "cpu"
    if device == "cpu":
        print("CUDA is not available. Image generation requires a GPU.")
        return

    print("\n--- Initializing Stable Diffusion Pipeline (this may take a moment) ---")
    context = Context()
    context.device = device
    context.half_precision = True  # Recommended for SDXL
    if seed is not None:
        torch.manual_seed(seed)
    try:
        context.model_paths["stable-diffusion"] = SD_MODELS_DIR_PATH[model_name]
        context.model_paths["vae"] = VAE_PATH
        load_model(context, "stable-diffusion")
        load_model(context, "vae")
        print("Pipeline initialized successfully.")
    except Exception as e:
        print(f"Error initializing Diffusion Pipeline: {e}")
        return

    image_base_path = os.path.join(SRC_DIR, 'assets', 'images')
    start_time = time.perf_counter()

    # Generate category thumbnails (no LoRA) with higher guidance for vibrant colors
    cats_generated = generate_category_thumbnails(context, category_data, image_base_path, variants, steps_thumb, guidance, sampler_name, force_overwrite, seed)

    end_time = time.perf_counter()
    duration = end_time - start_time
    total_items_generated = cats_generated

    if total_items_generated > 0:
        minutes, seconds = divmod(duration, 60)
        print("\n--- Thumbnail Generation Complete ---")
        print(f"Generated a total of {total_items_generated * variants} thumbnail(s).")
        print(f"Total time taken: {int(minutes)} minutes and {seconds:.2f} seconds.")
    else:
        print("\n--- No new thumbnails to generate. All items are up to date. ---")

def parse_float_list(value_string):
    """Parse comma-separated float values"""
    try:
        return [float(x.strip()) for x in value_string.split(',') if x.strip()]
    except ValueError as e:
        raise ValueError(f"Invalid float values in '{value_string}': {e}")

def parse_string_list(value_string):
    """Parse comma-separated string values"""
    return [x.strip() for x in value_string.split(',') if x.strip()]

def parse_int_list(value_string):
    """Parse comma-separated integer values"""
    try:
        return [int(x.strip()) for x in value_string.split(',') if x.strip()]
    except ValueError as e:
        raise ValueError(f"Invalid integer values in '{value_string}': {e}")

def test_parameter_grid(context, item_data, image_base_path, item_type="page", seed=None,
                       guidance_values=None, lora_values=None, samplers=None, steps_values=None, model_name=None):
    """Test a grid of parameters for a single page or category and save variants"""
    import torch
    from sdkit.generate import generate_images
    from PIL import Image, ImageDraw, ImageFont

    def create_grid_image(images, labels, save_path):
        """Create a grid image from individual images with parameter labels"""
        rows = len(images)
        cols = len(images[0]) if rows > 0 else 0

        if rows == 0 or cols == 0:
            print("No images to create grid from")
            return

        # Get image dimensions from first valid image
        sample_image = None
        for row in images:
            for img in row:
                if img is not None:
                    sample_image = img
                    break
            if sample_image:
                break

        if not sample_image:
            print("No valid images found for grid")
            return

        img_width, img_height = sample_image.size

        # Grid layout parameters
        padding = 10
        text_height = 50  # Increased for larger font
        cell_width = img_width + padding
        cell_height = img_height + text_height + padding

        # Create the grid image
        grid_width = cols * cell_width + padding
        grid_height = rows * cell_height + padding
        grid_image = Image.new('RGB', (grid_width, grid_height), 'white')

        # Try to load a font
        font = None
        font_paths = [
            "arial.ttf", "Arial.ttf",
            "/System/Library/Fonts/Arial.ttf",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
            "C:/Windows/Fonts/arial.ttf"  # Windows
        ]

        for font_path in font_paths:
            try:
                font = ImageFont.truetype(font_path, 24)  # Font size
                break
            except:
                continue

        if not font:
            font = ImageFont.load_default()

        draw = ImageDraw.Draw(grid_image)

        # Paste images and add labels
        for row in range(rows):
            for col in range(cols):
                if images[row][col] is None:
                    continue

                # Calculate position
                x = col * cell_width + padding
                y = row * cell_height + padding

                # Paste the image
                grid_image.paste(images[row][col], (x, y))

                # Add label below the image
                label = labels[row][col]
                text_x = x + 2
                text_y = y + img_height + 5
                draw.text((text_x, text_y), label, fill='black', font=font)

        # Save the grid image
        grid_image.save(save_path)
        print(f"Parameter grid saved: {save_path}")

    # Use provided values or sensible defaults based on item type
    if item_type == "category":
        # Category thumbnails use different defaults (no LoRA, higher guidance)
        if guidance_values is None:
            guidance_values = [7.0, 9.0, 11.0]  # Higher guidance for vibrant thumbnails
        if lora_values is None:
            lora_values = [0.0]  # Categories don't use LoRA
        if steps_values is None:
            steps_values = [SD_DEFAULTS['steps_thumb']]  # Use steps_thumb for categories
        if samplers is None:
            samplers = [SD_DEFAULTS['sampler_thumb']]
    else:
        # Page defaults (existing behavior)
        if guidance_values is None:
            guidance_values = [5.0, 7.0, 9.0]
        if lora_values is None:
            lora_values = [0.5, 1.0]
        if steps_values is None:
            steps_values = [25]
        if samplers is None:
            samplers = ["euler_a"]

    # Set seed for reproducibility
    # Only set torch seed if a specific seed is provided, otherwise generate a random seed for each image.
    if seed is not None:
        torch.manual_seed(seed)

    item_type_name = "Category" if item_type == "category" else "Page"
    print(f"\n--- Testing Parameter Grid for {item_type_name} '{item_data['slug']}' ---")
    print(f"Total combinations: {len(guidance_values) * len(lora_values) * len(steps_values) * len(samplers)}")

    # Set up target directory and prompts based on item type
    if item_type == "category":
        # Categories are saved directly in the image base path
        if 'parent_slug' in item_data:
            # This is a subcategory, so save in its parent's directory
            target_dir = os.path.join(image_base_path, item_data['parent_slug'])
        else:
            # This is a main category
            target_dir = image_base_path
        os.makedirs(target_dir, exist_ok=True)
        positive_prompt = CATEGORY_POSITIVE_PROMPT.format(prompt=item_data['prompt'])
        negative_prompt = CATEGORY_NEGATIVE_PROMPT
        width, height = 1024, 1024  # Fixed size for category thumbnails
    else:
        # Pages are saved in subdirectories
        target_dir = os.path.join(image_base_path, item_data['parent-category'], item_data['category'])
        os.makedirs(target_dir, exist_ok=True)
        positive_prompt = PAGE_POSITIVE_PROMPT.format(
            subject=item_data['prompt'],
            camera_angle=item_data['camera-angle'],
            camera_shot=item_data['camera-shot']
        )
        negative_prompt = PAGE_NEGATIVE_PROMPT
        width, height = ORIENTATIONS.get(item_data.get('orientation', 'portrait').lower(), ORIENTATIONS['portrait'])

    # Prepare grid storage: organize by guidance columns
    # Create combinations for rows based on item type
    row_combinations = []
    if item_type == "category":
        # Categories don't use LoRA, so skip the LoRA loop
        for steps in steps_values:
            for sampler_name in samplers:
                row_combinations.append((0.0, steps, sampler_name))  # Use 0.0 as placeholder for LoRA
    else:
        # Pages use LoRA, so include the LoRA loop
        for lora in lora_values:
            for steps in steps_values:
                for sampler_name in samplers:
                    row_combinations.append((lora, steps, sampler_name))

    # Initialize grid storage
    grid_images = []
    grid_labels = []
    variant_count = 0

    # Generate images organized by grid layout
    for lora, steps, sampler_name in row_combinations:
        image_row = []
        label_row = []

        for guidance in guidance_values:
            variant_count += 1

            # Generate a new random seed for each image if not provided
            current_seed = seed if seed is not None else random.randint(0, 2**32 - 1)

            # Create filename and label based on item type
            if item_type == "category":
                filename = f"{item_data['slug']}_{model_name}_{sampler_name}_s{steps}_g{guidance}.png"  # No LoRA in filename
                label = f"{model_name} {sampler_name} s{steps} g{guidance}"  # No LoRA for categories
            else:
                filename = f"{item_data['slug']}_{model_name}_{sampler_name}_s{steps}_l{lora}_g{guidance}.png"
                label = f"{model_name} {sampler_name} s{steps} l{lora} g{guidance}"

            save_path = os.path.join(target_dir, filename)

            print(f"  -> Generating variant {variant_count}: guidance={guidance}, lora={lora}, steps={steps}, sampler={sampler_name}")

            try:
                # Generate image with appropriate parameters for item type
                if item_type == "category":
                    image = generate_images(
                        context,
                        prompt=positive_prompt,
                        negative_prompt=negative_prompt,
                        num_inference_steps=steps,
                        guidance_scale=guidance,
                        lora_alpha=0.0,  # No LoRA for categories
                        width=width,
                        height=height,
                        num_outputs=1,
                        sampler_name=sampler_name,
                    )[0]
                else:
                    image = generate_images(
                        context,
                        prompt=positive_prompt,
                        negative_prompt=negative_prompt,
                        num_inference_steps=steps,
                        guidance_scale=guidance,
                        lora_alpha=lora,
                        width=width,
                        height=height,
                        num_outputs=1,
                        sampler_name=sampler_name,
                    )[0]

                # Save individual image
                image.save(save_path)
                print(f"     Saved: {filename}")

                # Store for grid
                image_row.append(image)
                label_row.append(label)

            except Exception as e:
                print(f"     Error generating {filename}: {e}")
                # Add None placeholder for failed generations
                image_row.append(None)
                label_row.append(label)

        grid_images.append(image_row)
        grid_labels.append(label_row)

    # Create and save the parameter grid image
    grid_filename = f"{item_data['slug']}_{model_name}_grid.png"
    grid_save_path = os.path.join(target_dir, grid_filename)
    create_grid_image(grid_images, grid_labels, grid_save_path)

    print(f"\nGrid testing complete! Generated {variant_count} variants.")
    print(f"Individual images and parameter grid saved in: {target_dir}")
    return variant_count

# --- MAIN EXECUTION BLOCK ---
if __name__ == "__main__":
    script_name = os.path.basename(sys.argv[0])
    help_epilog = f"""
Examples:

  1. Synchronize all content files and folders with the CSVs:
     python {script_name} content

  2. Generate missing coloring page images with default settings:
     python {script_name} images

  3. Generate 3 variants for each missing coloring page image, overwriting existing ones:
     python {script_name} images --variants 3 --force

  4. Generate missing coloring page images with custom settings and a specific model:
     python {script_name} images --steps 30 --guidance 6.5 --lora 1.0 --model sdxl

  5. Generate missing category thumbnail images with default settings:
     python {script_name} thumbnails

  6. Generate 2 variants for each missing category thumbnail, overwriting existing ones:
     python {script_name} thumbnails --variants 2 --force

  7. Generate missing category thumbnail images with custom settings and a specific model:
     python {script_name} thumbnails --steps 25 --guidance 10.0 --model cyberrealisticxl

  8. Test parameter grid for a coloring page (uses LoRA) with a specific model:
     python {script_name} test --page vibrant-parrot-in-the-jungle-coloring-page --model sdxl

  9. Test parameter grid for a category thumbnail (no LoRA) with a specific model:
     python {script_name} test --page animals-wildlife --model cyberrealisticxl

  10. Test with custom parameter grids for pages:
      python {script_name} test --page vibrant-parrot-in-the-jungle-coloring-page --guidance 5.0,8.0,12.0 --lora 0.3,0.7,1.2 --sampler euler_a

  11. Test with custom parameter grids for categories:
      python {script_name} test --page animals-wildlife --guidance 7.0,9.0,12.0 --sampler euler_a
"""
    parser = argparse.ArgumentParser(description="A tool to synchronize content and generate images for the coloring page site.", formatter_class=argparse.RawTextHelpFormatter, epilog=help_epilog)
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    content_parser = subparsers.add_parser("content", help="Synchronize folders and markdown files based on CSVs.")
    images_parser = subparsers.add_parser("images", help="Generate coloring page images for missing files.")
    images_parser.add_argument("--variants", type=int, default=1, help="Number of image variants to generate for each missing coloring page.")
    images_parser.add_argument("--steps", type=int, default=SD_DEFAULTS['steps'], help=f"Number of inference steps (default: {SD_DEFAULTS['steps']}).")
    images_parser.add_argument("--guidance", type=float, default=SD_DEFAULTS['guidance'], help=f"Guidance scale for coloring pages (default: {SD_DEFAULTS['guidance']}).")
    images_parser.add_argument("--lora", type=float, default=SD_DEFAULTS['lora_scale'], dest='lora_scale', help=f"LoRA scale/weight (default: {SD_DEFAULTS['lora_scale']}).")
    images_parser.add_argument("--model", type=str, default=SD_DEFAULTS['model'], help=f"Stable Diffusion model to use for coloring page image generation (default: {SD_DEFAULTS['model']}).")
    images_parser.add_argument("--force", action="store_true", help="Force overwrite of existing coloring page images.")
    # Common samplers - sdkit supports many more, these are just popular ones
    common_samplers = ["euler_a", "euler", "ddim", "heun", "dpmpp_2m", "dpmpp_2m_sde", "dpm2", "dpm2_a", "lms", "plms"]
    images_parser.add_argument("--sampler", type=str, default=SD_DEFAULTS['sampler'], help=f"Sampler to use for coloring page image generation (default: {SD_DEFAULTS['sampler']}). Common options: {', '.join(common_samplers)}")
    images_parser.add_argument("--seed", type=int, default=None, help="Seed for reproducible image generation (default: random).")
    thumbnails_parser = subparsers.add_parser("thumbnails", help="Generate category thumbnail images for missing files.")
    thumbnails_parser.add_argument("--variants", type=int, default=1, help="Number of image variants to generate for each missing thumbnail.")
    thumbnails_parser.add_argument("--steps", type=int, default=SD_DEFAULTS['steps_thumb'], help=f"Number of inference steps (default: {SD_DEFAULTS['steps_thumb']}).")
    thumbnails_parser.add_argument("--guidance", type=float, default=SD_DEFAULTS['guidance_thumb'], help=f"Guidance scale for category thumbnails (default: {SD_DEFAULTS['guidance_thumb']}).")
    thumbnails_parser.add_argument("--model", type=str, default=SD_DEFAULTS['model_thumb'], help=f"Stable Diffusion model to use for category thumbnail generation (default: {SD_DEFAULTS['model_thumb']}).")
    thumbnails_parser.add_argument("--force", action="store_true", help="Force overwrite of existing category thumbnail images.")
    thumbnails_parser.add_argument("--sampler", type=str, default=SD_DEFAULTS['sampler_thumb'], help=f"Sampler to use for category thumbnail generation (default: {SD_DEFAULTS['sampler_thumb']}). Common options: {', '.join(common_samplers)}")
    thumbnails_parser.add_argument("--seed", type=int, default=None, help="Seed for reproducible image generation (default: random).")
    test_parser = subparsers.add_parser("test", help="Test image generation with a grid of parameters for a single page or category.")
    test_parser.add_argument("--seed", type=int, default=64738, help="Seed for reproducible image generation (default: 64738).")
    test_parser.add_argument("--page", type=str, default="vibrant-parrot-in-the-jungle-coloring-page", help="Slug of the page or category to test (default: vibrant-parrot-in-the-jungle-coloring-page)")
    test_parser.add_argument("--guidance", type=str, default="5.0,7.0,9.0", help="Comma-separated guidance scale values to test (default: 5.0,7.0,9.0 for pages; 7.0,9.0,11.0 for categories)")
    test_parser.add_argument("--lora", type=str, default="0.5,1.0", help="Comma-separated LoRA alpha values to test (default: 0.5,1.0; ignored for categories)")
    test_parser.add_argument("--steps", type=str, default="25", help="Comma-separated inference step values to test (default: 25)")
    test_parser.add_argument("--sampler", type=str, default="euler_a", help="Comma-separated sampler names to test (default: euler_a)")
    test_parser.add_argument("--model", type=parse_string_list, default=SD_DEFAULTS['model'], help=f"Comma-separated Stable Diffusion models to use for testing (default: {SD_DEFAULTS['model']} for pages, {SD_DEFAULTS['model_thumb']} for categories).")
    args = parser.parse_args()
    if not args.command:
        parser.print_help()
        sys.exit(0)

    category_data = load_data_from_csv(CATEGORIES_CSV)
    pages_data = load_pages_data_from_csv(PAGES_CSV)

    if args.command == "content":
        print("Running in Content Sync mode.")
        if category_data:
            md_path = os.path.join(SRC_DIR, 'content', 'categories')
            coloring_pages_path = os.path.join(SRC_DIR, 'content', 'coloring-pages')
            image_path = os.path.join(SRC_DIR, 'assets', 'images')
            expected_md, expected_coloring = get_expected_paths(category_data, pages_data, md_path, coloring_pages_path)
            expected_images = get_expected_image_paths(category_data, pages_data, image_path)
            cleanup_orphans(md_path, expected_md)
            cleanup_orphans(coloring_pages_path, expected_coloring)
            cleanup_orphan_images(image_path, expected_images)
            sync_category_markdown(md_path, category_data)
            sync_directory_structure(coloring_pages_path, category_data)
            sync_directory_structure(image_path, category_data)
            if pages_data:
                sync_page_markdown(coloring_pages_path, pages_data)
            print("\nSynchronization complete! Filesystem is now in sync with CSV data.")
        else:
            print("Could not load category data. Aborting script.")

    elif args.command == "images":
        print("Running in Image Generation mode.")
        if category_data and pages_data:
            run_images_command(
                category_data,
                pages_data,
                variants=args.variants,
                steps=args.steps,
                guidance=args.guidance,
                lora_scale=args.lora_scale,
                sampler_name=args.sampler,
                model_name=args.model,
                force_overwrite=args.force,
                seed=args.seed
            )
        else:
            print("Could not load category and/or pages data. Aborting image generation.")

    elif args.command == "thumbnails":
        print("Running in Thumbnail Generation mode.")
        if category_data:
            run_thumbnails_command(
                category_data,
                variants=args.variants,
                steps_thumb=args.steps,
                guidance=args.guidance,
                sampler_name=args.sampler,
                model_name=args.model,
                force_overwrite=args.force,
                seed=args.seed
            )
        else:
            print("Could not load category data. Aborting thumbnail generation.")

    elif args.command == "test":
        image_base_path = os.path.join(SRC_DIR, 'assets', 'images')

        # Find the item to test (page or category), defaulting to the specified slug
        item_slug = args.page if hasattr(args, 'page') and args.page else "vibrant-parrot-in-the-jungle-coloring-page"

        # First try to find it as a page
        item_to_test = next((page for page in pages_data if page['slug'] == item_slug), None)
        item_type = "page"

        # If not found as a page, try to find it as a category
        if not item_to_test:
            # Check parent categories
            item_to_test = next((cat for cat in category_data if cat['slug'] == item_slug), None)
            if item_to_test:
                item_type = "category"
            else:
                # Check subcategories
                for cat in category_data:
                    item_to_test = next((sub for sub in cat['subcategories'] if sub['slug'] == item_slug), None)
                    if item_to_test:
                        item_type = "category"
                        item_to_test['parent_slug'] = cat['slug'] # Add parent slug for subcategories
                        break

        if not item_to_test:
            print(f"Error: Could not find page or category with slug '{item_slug}' in CSV data.")
            sys.exit(1)

        print(f"Found {item_type}: '{item_to_test['slug']}'")

        import torch
        from sdkit import Context
        from sdkit.models import load_model
        device = "cuda" if torch.cuda.is_available() else "cpu"
        if device == "cpu":
            print("CUDA is not available. Image generation requires a GPU.")
            sys.exit(1)

        # Parse parameter grids from command line arguments
        try:
            guidance_values = parse_float_list(args.guidance)
            lora_values = parse_float_list(args.lora)
            samplers = parse_string_list(args.sampler)
            steps_values = parse_int_list(args.steps)
        except ValueError as e:
            print(f"Error parsing parameter values: {e}")
            sys.exit(1)

        for model_to_test in args.model:
            print(f"\n--- Testing with model: {model_to_test} ---")
            print("\n--- Initializing Stable Diffusion Pipeline (this may take a moment) ---")
            context = Context()
            context.device = device
            context.half_precision = True  # Recommended for SDXL
            try:
                context.model_paths["stable-diffusion"] = SD_MODELS_DIR_PATH[model_to_test]
                context.model_paths["vae"] = VAE_PATH
                load_model(context, "stable-diffusion")
                load_model(context, "vae")
                print("Pipeline initialized successfully.")
            except Exception as e:
                print(f"Error initializing Diffusion Pipeline with {model_to_test}: {e}")
                sys.exit(1)

            # Load LoRA only for pages (categories don't use LoRA)
            if item_type == "page":
                context.model_paths["lora"] = LORA_PATH
                load_model(context, "lora")

            # Test the grid
            test_parameter_grid(context, item_to_test, image_base_path, item_type=item_type, seed=args.seed,
                                guidance_values=guidance_values, lora_values=lora_values,
                                samplers=samplers, steps_values=steps_values, model_name=model_to_test)